/* Emma Studio Professional Photography Effects */
/* Brand Colors: #3018ef (blue), #dd3a5a (red/pink) */

/* Studio Lighting Animation */
@keyframes studioFlash {
  0% { opacity: 0; }
  10% { opacity: 0.8; }
  20% { opacity: 0; }
  100% { opacity: 0; }
}

@keyframes apertureSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes focusRing {
  0% { 
    transform: scale(1);
    opacity: 0.6;
  }
  50% { 
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% { 
    transform: scale(1);
    opacity: 0.6;
  }
}

/* Studio Environment */
.studio-environment {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.studio-environment::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(0, 0, 0, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* Viewfinder Effects */
.viewfinder-frame {
  background: #000;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 
    inset 0 0 0 2px rgba(255, 255, 255, 0.1),
    0 8px 32px rgba(0, 0, 0, 0.3);
}

.viewfinder-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, transparent 49%, rgba(255, 255, 255, 0.02) 50%, transparent 51%),
    linear-gradient(-45deg, transparent 49%, rgba(255, 255, 255, 0.02) 50%, transparent 51%);
  background-size: 20px 20px;
  pointer-events: none;
  opacity: 0.3;
}

/* Focus Point */
.focus-point {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  position: relative;
}

.focus-point::before {
  content: '';
  position: absolute;
  inset: -4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: focusRing 2s ease-in-out infinite;
}

.focus-point.active {
  border-color: #dd3a5a;
  animation: focusRing 0.5s ease-in-out infinite;
}

/* Studio Flash Effect */
.studio-flash {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, rgba(48, 24, 239, 0.3) 0%, rgba(221, 58, 90, 0.2) 50%, transparent 70%);
  animation: studioFlash 0.8s ease-out;
  pointer-events: none;
}

/* Professional UI Elements */
.studio-control {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.studio-control:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Lighting Setup Buttons */
.lighting-preset {
  position: relative;
  overflow: hidden;
}

.lighting-preset::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.lighting-preset:hover::before {
  left: 100%;
}

/* Film Strip Effect */
.film-strip {
  background: #1a1a1a;
  position: relative;
  padding: 16px;
  border-radius: 8px;
}

.film-strip::before {
  content: '';
  position: absolute;
  top: 8px;
  bottom: 8px;
  left: 8px;
  right: 8px;
  border: 1px dashed rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  pointer-events: none;
}

/* Professional Typography */
.studio-mono {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 400;
  letter-spacing: 0.05em;
}

/* Capture Button Effect */
.capture-button {
  position: relative;
  overflow: hidden;
}

.capture-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(221, 58, 90, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.capture-button:active::before {
  width: 300px;
  height: 300px;
}

/* Status Indicators */
.status-indicator {
  position: relative;
}

.status-indicator.recording::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(221, 58, 90, 0.1);
  border-radius: inherit;
  animation: pulse 1s ease-in-out infinite;
}

/* Smooth Transitions */
.studio-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glass Morphism Effects */
.glass-panel {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Professional Grid Overlay */
.grid-overlay {
  position: absolute;
  inset: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.show-grid .grid-overlay {
  opacity: 1;
}

/* Loading States */
.processing-dots {
  display: inline-flex;
  gap: 4px;
}

.processing-dots > div {
  width: 8px;
  height: 8px;
  background: currentColor;
  border-radius: 50%;
  animation: processingPulse 1.4s ease-in-out infinite both;
}

.processing-dots > div:nth-child(1) { animation-delay: -0.32s; }
.processing-dots > div:nth-child(2) { animation-delay: -0.16s; }

@keyframes processingPulse {
  0%, 80%, 100% { 
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% { 
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1280px) {
  .viewfinder-frame {
    aspect-ratio: 16/10;
  }
}

@media (max-width: 768px) {
  .studio-control {
    padding: 12px;
  }
  
  .viewfinder-frame {
    aspect-ratio: 4/3;
  }
}
