/* Emma Studio Dashboard - Minimal Professional Styling */

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f8fafc;
}

::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}

/* Smooth transitions for all interactive elements */
.dashboard-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Professional button hover states */
.btn-minimal {
  transition: all 0.15s ease-in-out;
  font-weight: 500;
  letter-spacing: -0.01em;
}

.btn-minimal:hover {
  transform: translateY(-1px);
}

/* Subtle focus states */
.focus-minimal:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(48, 24, 239, 0.1);
  border-color: #3018ef;
}

/* Grid system with proper spacing */
.grid-minimal {
  display: grid;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .grid-minimal {
    gap: 2rem;
  }
}

/* Typography improvements */
.text-minimal-heading {
  font-weight: 600;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.text-minimal-body {
  font-weight: 400;
  letter-spacing: -0.01em;
  line-height: 1.5;
}

/* Professional shadows */
.shadow-minimal {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.shadow-minimal-hover:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Gradient overlays for premium feel */
.gradient-overlay {
  position: relative;
}

.gradient-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(48, 24, 239, 0.02) 0%, rgba(221, 58, 90, 0.02) 100%);
  pointer-events: none;
  border-radius: inherit;
}

/* Professional loading states */
.loading-minimal {
  background: linear-gradient(90deg, #f8fafc 25%, #f1f5f9 50%, #f8fafc 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Micro-interactions */
.micro-bounce:hover {
  animation: micro-bounce 0.3s ease-in-out;
}

@keyframes micro-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Professional borders */
.border-minimal {
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.border-minimal-hover:hover {
  border-color: rgba(203, 213, 225, 0.8);
}

/* Status indicators */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-success {
  background-color: #10b981;
}

.status-warning {
  background-color: #f59e0b;
}

.status-info {
  background-color: #3b82f6;
}

.status-error {
  background-color: #ef4444;
}

/* Professional spacing system (8px grid) */
.space-minimal-xs { margin: 0.5rem; }
.space-minimal-sm { margin: 1rem; }
.space-minimal-md { margin: 1.5rem; }
.space-minimal-lg { margin: 2rem; }
.space-minimal-xl { margin: 3rem; }

/* Responsive typography */
@media (max-width: 768px) {
  .text-minimal-heading {
    font-size: 1.25rem;
  }
}

/* Professional card layouts */
.card-minimal {
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
}

.card-minimal:hover {
  border-color: rgba(203, 213, 225, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

/* Icon containers */
.icon-container-minimal {
  width: 2.5rem;
  height: 2.5rem;
  background: #f8fafc;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
}

.icon-container-minimal:hover {
  background: #3018ef;
  border-color: #3018ef;
  color: white;
}

/* Progress bars */
.progress-minimal {
  width: 100%;
  height: 0.375rem;
  background: #f1f5f9;
  border-radius: 0.1875rem;
  overflow: hidden;
}

.progress-fill-minimal {
  height: 100%;
  background: #3018ef;
  border-radius: 0.1875rem;
  transition: width 0.3s ease-in-out;
}

/* Professional animations */
.fade-in-minimal {
  animation: fade-in-minimal 0.4s ease-out;
}

@keyframes fade-in-minimal {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Glassmorphism effect for premium feel */
.glass-minimal {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Professional color palette with strategic enhancements */
:root {
  --emma-blue: #3018ef;
  --emma-blue-hover: #2612d4;
  --emma-blue-light: #6366f1;
  --emma-blue-bg: rgba(48, 24, 239, 0.05);
  --emma-pink: #dd3a5a;
  --emma-pink-light: #f472b6;
  --emma-pink-bg: rgba(221, 58, 90, 0.05);
  --emma-purple: #8b5cf6;
  --emma-purple-bg: rgba(139, 92, 246, 0.05);
  --emma-green: #10b981;
  --emma-green-bg: rgba(16, 185, 129, 0.05);
  --emma-orange: #f59e0b;
  --emma-orange-bg: rgba(245, 158, 11, 0.05);
  --emma-cyan: #06b6d4;
  --emma-cyan-bg: rgba(6, 182, 212, 0.05);
  --emma-gray-50: #f8fafc;
  --emma-gray-100: #f1f5f9;
  --emma-gray-200: #e2e8f0;
  --emma-gray-300: #cbd5e1;
  --emma-gray-400: #94a3b8;
  --emma-gray-500: #64748b;
  --emma-gray-600: #475569;
  --emma-gray-700: #334155;
  --emma-gray-800: #1e293b;
  --emma-gray-900: #0f172a;
}

/* Beautiful gradient text for headings */
.gradient-text-emma {
  background: linear-gradient(135deg, var(--emma-blue) 0%, var(--emma-pink) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-blue {
  background: linear-gradient(135deg, var(--emma-blue) 0%, var(--emma-blue-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Colored icon containers */
.icon-container-blue {
  background: var(--emma-blue-bg);
  color: var(--emma-blue);
  border: 1px solid rgba(48, 24, 239, 0.1);
}

.icon-container-blue:hover {
  background: var(--emma-blue);
  color: white;
  border-color: var(--emma-blue);
}

.icon-container-green {
  background: var(--emma-green-bg);
  color: var(--emma-green);
  border: 1px solid rgba(16, 185, 129, 0.1);
}

.icon-container-green:hover {
  background: var(--emma-green);
  color: white;
  border-color: var(--emma-green);
}

.icon-container-purple {
  background: var(--emma-purple-bg);
  color: var(--emma-purple);
  border: 1px solid rgba(139, 92, 246, 0.1);
}

.icon-container-purple:hover {
  background: var(--emma-purple);
  color: white;
  border-color: var(--emma-purple);
}

.icon-container-orange {
  background: var(--emma-orange-bg);
  color: var(--emma-orange);
  border: 1px solid rgba(245, 158, 11, 0.1);
}

.icon-container-orange:hover {
  background: var(--emma-orange);
  color: white;
  border-color: var(--emma-orange);
}

.icon-container-pink {
  background: var(--emma-pink-bg);
  color: var(--emma-pink);
  border: 1px solid rgba(221, 58, 90, 0.1);
}

.icon-container-pink:hover {
  background: var(--emma-pink);
  color: white;
  border-color: var(--emma-pink);
}

.icon-container-cyan {
  background: var(--emma-cyan-bg);
  color: var(--emma-cyan);
  border: 1px solid rgba(6, 182, 212, 0.1);
}

.icon-container-cyan:hover {
  background: var(--emma-cyan);
  color: white;
  border-color: var(--emma-cyan);
}

/* Subtle colored card backgrounds */
.card-tinted-blue {
  background: linear-gradient(135deg, white 0%, var(--emma-blue-bg) 100%);
}

.card-tinted-pink {
  background: linear-gradient(135deg, white 0%, var(--emma-pink-bg) 100%);
}

.card-tinted-purple {
  background: linear-gradient(135deg, white 0%, var(--emma-purple-bg) 100%);
}

/* Enhanced progress bars with colors */
.progress-blue .progress-fill-minimal {
  background: linear-gradient(90deg, var(--emma-blue) 0%, var(--emma-blue-light) 100%);
}

.progress-pink .progress-fill-minimal {
  background: linear-gradient(90deg, var(--emma-pink) 0%, var(--emma-pink-light) 100%);
}

.progress-purple .progress-fill-minimal {
  background: linear-gradient(90deg, var(--emma-purple) 0%, var(--emma-blue) 100%);
}
