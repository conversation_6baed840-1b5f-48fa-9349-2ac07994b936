# Emma Studio Landing Page Backup
**Fecha de creación:** 21 de junio de 2025  
**Hora de backup:** $(date)  
**Versión:** Current Production State

## Descripción
Esta copia de seguridad contiene el estado completo actual de la landing page de Emma Studio antes de realizar modificaciones. Incluye todos los archivos principales, componentes y estilos relacionados con la landing page.

## Estructura del Backup

### 📄 Páginas Principales (`/pages/`)
- `landing-page-backup-*.tsx` - Página principal de landing (ruta: `/`)
- `home-backup-*.tsx` - Página de inicio alternativa
- `emma-landing-v2-backup-*.tsx` - Landing page versión 2

### 🧩 Componentes (`/components/`)
Todos los componentes de la landing page ubicados originalmente en `client/src/components/landing/`:

#### Componentes Principales:
- `header.tsx` - Cabecera con navegación
- `hero.tsx` - Sección hero principal
- `footer.tsx` / `new-footer.tsx` - Pie de página

#### Secciones de Contenido:
- `value-proposition.tsx` - Propuesta de valor
- `automated-workforce.tsx` - Fuerza laboral automatizada
- `problem-solution.tsx` - Problema y solución
- `how-it-works-new.tsx` - Cómo funciona
- `agent-showcase.tsx` - Showcase de agentes
- `marketing-tools.tsx` - Herramientas de marketing
- `ai-studios.tsx` - Estudios de IA
- `human-services.tsx` - Servicios humanos
- `comparison.tsx` - Comparaciones
- `agent-detail.tsx` - Detalles de agentes
- `testimonials.tsx` - Testimonios
- `pricing.tsx` - Precios
- `faq.tsx` - Preguntas frecuentes
- `cta.tsx` - Call to action

#### Componentes de Soporte:
- `preloader.tsx` - Precargador
- `intro-animation.tsx` - Animaciones de introducción
- `splash-intro.tsx` - Intro splash
- `features.tsx` - Características
- `use-cases.tsx` - Casos de uso

### 🎨 Estilos (`/styles/`)
- `studio-effects-backup-*.css` - Efectos específicos del estudio
- `dashboard-minimal-backup-*.css` - Estilos del dashboard y colores de marca

## Características Técnicas

### Stack Tecnológico:
- **Framework:** React con TypeScript
- **Routing:** Wouter
- **Animaciones:** Framer Motion
- **Estilos:** Tailwind CSS + CSS personalizado
- **Iconos:** Lucide React

### Colores de Marca:
- **Azul Emma:** `#3018ef`
- **Rojo/Rosa Emma:** `#dd3a5a`
- **Efectos:** Glassmorphism

### Funcionalidades Clave:
- Diseño responsivo completo
- Animaciones fluidas con Framer Motion
- Navegación con Wouter
- Efectos glassmorphism
- Componentes modulares siguiendo principios SOLID
- Interfaz completamente en español

## Instrucciones de Restauración

### Para restaurar la landing page completa:

1. **Restaurar páginas principales:**
   ```bash
   cp backups/landing-pages/2025-06-21-current/pages/* client/src/pages/
   ```

2. **Restaurar componentes:**
   ```bash
   cp -r backups/landing-pages/2025-06-21-current/components/* client/src/components/landing/
   ```

3. **Restaurar estilos:**
   ```bash
   cp backups/landing-pages/2025-06-21-current/styles/* client/src/styles/
   ```

### Para restaurar archivos específicos:
- Renombrar los archivos eliminando el sufijo `-backup-*` del timestamp
- Copiar al directorio original correspondiente

## Notas Importantes

- ✅ **Backup completo:** Incluye todos los archivos relacionados con la landing page
- ✅ **Timestamped:** Cada archivo tiene timestamp único para evitar conflictos
- ✅ **Estructura preservada:** Mantiene la organización original del proyecto
- ✅ **Dependencias incluidas:** Todos los componentes y estilos necesarios
- ⚠️ **Rutas:** Verificar que las rutas de importación sean correctas al restaurar
- ⚠️ **Assets:** Los assets/imágenes no están incluidos en este backup

## Archivos Relacionados No Incluidos

Los siguientes archivos pueden estar relacionados pero no se incluyeron en este backup:
- Assets/imágenes en `client/src/assets/`
- Configuraciones de routing en `client/src/App.tsx`
- Hooks personalizados en `client/src/hooks/`
- Utilidades en `client/src/lib/`

## Contacto
Para preguntas sobre este backup o restauración, consultar la documentación del proyecto Emma Studio.

---
**Generado automáticamente el:** $(date)  
**Ubicación original:** `/Users/<USER>/emma-studio-`
