import { useState, useRef, useEffect } from "react";
import { Link } from "wouter";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowR<PERSON>,
  <PERSON>rkles,
  Clock,
  CheckCircle,
  BrainCircuit,
  Bot,
  Rocket,
  BarChart3,
  Paintbrush,
  Code,
  LayoutGrid,
  MessageSquare,
  FileText,
  Instagram,
  Facebook,
  Linkedin,
  Globe,
  SearchCode,
  TrendingUp,
  ShoppingCart,
  Mail,
  Send,
  ChevronRight,
  MousePointer,
  Zap,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

// Layout principal para Emma AI con tema moderno
const EmmaLayout = ({ children }: { children: React.ReactNode }) => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      <header className="border-b border-gray-100 sticky top-0 z-50 bg-white/95 backdrop-blur-sm">
        <div className="container mx-auto py-4 px-4">
          <div className="flex justify-between items-center">
            <div className="text-2xl font-bold text-purple-600 cursor-pointer">
              EMMA
              <span className="ml-2 text-sm font-light text-gray-500">
                AI MARKETING
              </span>
            </div>

            {/* Menú escritorio */}
            <nav className="hidden md:flex items-center space-x-8 text-sm">
              <a href="#servicios" className="hover:text-purple-600 transition">
                Servicios
              </a>
              <a
                href="#como-funciona"
                className="hover:text-purple-600 transition"
              >
                Cómo Funciona
              </a>
              <a href="#casos" className="hover:text-purple-600 transition">
                Casos de Éxito
              </a>
              <a href="#precios" className="hover:text-purple-600 transition">
                Precios
              </a>
              <Button
                variant="ghost"
                className="text-gray-600 hover:text-purple-600"
                onClick={() => (window.location.href = "/app")}
              >
                Regresar a Dashboard
              </Button>
              <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                <Sparkles className="h-4 w-4 mr-2" />
                Comenzar
              </Button>
            </nav>

            {/* Botón menú móvil */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </Button>
          </div>

          {/* Menú móvil desplegable */}
          {mobileMenuOpen && (
            <div className="md:hidden mt-4 pb-4 space-y-4 transition-all duration-300">
              <a
                href="#servicios"
                className="block py-2 text-gray-900 hover:text-purple-600"
              >
                Servicios
              </a>
              <a
                href="#como-funciona"
                className="block py-2 text-gray-900 hover:text-purple-600"
              >
                Cómo Funciona
              </a>
              <a
                href="#casos"
                className="block py-2 text-gray-900 hover:text-purple-600"
              >
                Casos de Éxito
              </a>
              <a
                href="#precios"
                className="block py-2 text-gray-900 hover:text-purple-600"
              >
                Precios
              </a>
              <Button
                className="w-full mt-2 bg-purple-600 hover:bg-purple-700 text-white"
                onClick={() => (window.location.href = "/app")}
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Comenzar
              </Button>
            </div>
          )}
        </div>
      </header>

      <main>{children}</main>

      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between">
            <div className="mb-8 md:mb-0">
              <div className="text-2xl font-bold text-purple-400 mb-4">
                EMMA
                <span className="ml-2 text-sm font-light text-gray-400">
                  AI MARKETING
                </span>
              </div>
              <p className="text-gray-400 max-w-md">
                La primera plataforma de marketing totalmente automatizada con
                inteligencia artificial que hace todo el trabajo por ti.
              </p>
              <div className="flex mt-4 space-x-4">
                <a href="#" className="text-gray-400 hover:text-white">
                  <Instagram size={20} />
                </a>
                <a href="#" className="text-gray-400 hover:text-white">
                  <Facebook size={20} />
                </a>
                <a href="#" className="text-gray-400 hover:text-white">
                  <Linkedin size={20} />
                </a>
                <a href="#" className="text-gray-400 hover:text-white">
                  <Globe size={20} />
                </a>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
              <div>
                <h3 className="text-gray-200 font-semibold mb-4">Servicios</h3>
                <ul className="space-y-2 text-gray-400">
                  <li>
                    <a href="#" className="hover:text-purple-400">
                      Copywriting
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-400">
                      Email Marketing
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-400">
                      Social Media
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-400">
                      SEO
                    </a>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-gray-200 font-semibold mb-4">Empresa</h3>
                <ul className="space-y-2 text-gray-400">
                  <li>
                    <a href="#" className="hover:text-purple-400">
                      Sobre Nosotros
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-400">
                      Blog
                    </a>
                  </li>
                  <li>
                    <a href="#" className="hover:text-purple-400">
                      Contacto
                    </a>
                  </li>
                </ul>
              </div>
              <div className="col-span-2 md:col-span-1">
                <h3 className="text-gray-200 font-semibold mb-4">Newsletter</h3>
                <p className="text-gray-400 mb-4">
                  Recibe tips de marketing potenciados por IA
                </p>
                <div className="flex">
                  <Input
                    placeholder="Tu email"
                    className="bg-gray-800 border-gray-700 text-white"
                  />
                  <Button className="ml-2 bg-purple-600 hover:bg-purple-700">
                    <Send size={16} />
                  </Button>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-800 text-center text-gray-500 text-sm">
            © {new Date().getFullYear()} Emma AI. Todos los derechos
            reservados.
          </div>
        </div>
      </footer>
    </div>
  );
};

// Componente para las tarjetas de servicios
const ServiceCard = ({
  title,
  icon: Icon,
  description,
  tags = [],
}: {
  title: string;
  icon: any;
  description: string;
  tags?: string[];
}) => {
  return (
    <Card className="border-2 border-gray-100 hover:border-purple-300 transition-all h-full shadow-sm hover:shadow-md overflow-hidden group">
      <CardContent className="p-6 pt-8">
        <div className="mb-5 p-3 inline-block rounded-lg bg-purple-100 text-purple-600 group-hover:bg-purple-600 group-hover:text-white transition-all">
          <Icon size={24} />
        </div>

        <CardTitle className="text-xl mb-3 group-hover:text-purple-600 transition-colors">
          {title}
        </CardTitle>
        <CardDescription className="text-gray-500 mb-5">
          {description}
        </CardDescription>

        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-auto">
            {tags.map((tag, index) => (
              <Badge
                key={index}
                variant="outline"
                className="bg-gray-50 text-gray-500 border-gray-200"
              >
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Componente para las tarjetas de testimonios
const TestimonialCard = ({
  name,
  role,
  company,
  testimonial,
  image,
}: {
  name: string;
  role: string;
  company: string;
  testimonial: string;
  image?: string;
}) => {
  return (
    <Card className="border-0 shadow-md h-full bg-gradient-to-br from-white to-purple-50">
      <CardContent className="p-8">
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 rounded-full bg-purple-200 flex items-center justify-center mr-4">
            {image ? (
              <img
                src={image}
                alt={name}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <span className="text-xl font-bold text-purple-700">
                {name.charAt(0)}
              </span>
            )}
          </div>
          <div>
            <h4 className="font-semibold">{name}</h4>
            <p className="text-sm text-gray-500">
              {role}, {company}
            </p>
          </div>
        </div>

        <p className="text-gray-600 italic">"{testimonial}"</p>

        <div className="mt-6 flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <svg
              key={star}
              className="w-5 h-5 text-yellow-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Componente para los casos de estudio
const CaseStudyCard = ({
  title,
  company,
  results,
  image,
  tags = [],
}: {
  title: string;
  company: string;
  results: string;
  image?: string;
  tags?: string[];
}) => {
  return (
    <Card className="overflow-hidden border-0 shadow-lg">
      <div className="h-48 bg-purple-200 relative overflow-hidden">
        {image ? (
          <img src={image} alt={title} className="w-full h-full object-cover" />
        ) : (
          <div className="flex items-center justify-center h-full text-purple-700">
            <BrainCircuit size={80} />
          </div>
        )}
      </div>
      <CardContent className="p-6">
        <Badge className="mb-2 bg-purple-100 text-purple-700 hover:bg-purple-200">
          {company}
        </Badge>
        <CardTitle className="text-xl mb-3">{title}</CardTitle>
        <CardDescription className="text-gray-600 mb-4">
          {results}
        </CardDescription>

        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {tags.map((tag, index) => (
              <Badge
                key={index}
                variant="outline"
                className="bg-gray-50 text-gray-500 border-gray-200"
              >
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="px-6 py-4 bg-gray-50 flex justify-between items-center">
        <span className="text-sm text-gray-500">Ver caso completo</span>
        <Button variant="ghost" size="sm" className="p-2 h-8 w-8 rounded-full">
          <ArrowRight size={16} />
        </Button>
      </CardFooter>
    </Card>
  );
};

// Componente para servicios en carrusel
const ServiceCarousel = ({ services }: { services: any[] }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const totalItems = services.length;
  const visibleItems = 3;
  const maxIndex = totalItems - visibleItems;

  const handlePrev = () => {
    setActiveIndex((prev) => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setActiveIndex((prev) => Math.min(maxIndex, prev + 1));
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prev) => {
        const next = prev + 1;
        return next > maxIndex ? 0 : next;
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [maxIndex]);

  return (
    <div className="relative">
      <div className="overflow-hidden">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{
            transform: `translateX(-${activeIndex * (100 / visibleItems)}%)`,
          }}
        >
          {services.map((service, index) => (
            <div key={index} className="w-full md:w-1/3 flex-shrink-0 p-3">
              <ServiceCard
                title={service.title}
                icon={service.icon}
                description={service.description}
                tags={service.tags}
              />
            </div>
          ))}
        </div>
      </div>

      <Button
        variant="outline"
        className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 rounded-full p-2 h-10 w-10 z-10 bg-white/90 border-gray-200 shadow-lg"
        onClick={handlePrev}
        disabled={activeIndex === 0}
      >
        <ChevronRight size={20} className="rotate-180" />
      </Button>

      <Button
        variant="outline"
        className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 rounded-full p-2 h-10 w-10 z-10 bg-white/90 border-gray-200 shadow-lg"
        onClick={handleNext}
        disabled={activeIndex === maxIndex}
      >
        <ChevronRight size={20} />
      </Button>

      <div className="flex justify-center mt-8 space-x-2">
        {Array.from({ length: maxIndex + 1 }).map((_, index) => (
          <button
            key={index}
            className={`h-2 rounded-full transition-all ${
              index === activeIndex ? "w-8 bg-purple-600" : "w-2 bg-gray-300"
            }`}
            onClick={() => setActiveIndex(index)}
          />
        ))}
      </div>
    </div>
  );
};

// Página principal de Emma AI Marketing
export default function EmmaLandingV2() {
  // Datos para servicio del carrusel
  const marketingServices = [
    {
      title: "Social Media Management",
      icon: Instagram,
      description:
        "Planificación, creación y publicación automática de contenido en todas tus redes sociales con optimización por plataforma.",
      tags: ["Instagram", "TikTok", "Facebook", "LinkedIn"],
    },
    {
      title: "Copywriting Perfecto",
      icon: FileText,
      description:
        "Textos persuasivos para anuncios, emails, landing pages y contenido web optimizados para conversión.",
      tags: ["Anuncios", "Landing pages", "Persuasión", "SEO"],
    },
    {
      title: "Email Marketing",
      icon: Mail,
      description:
        "Creación de campañas, segmentación inteligente, A/B testing automático y optimización de asuntos y contenidos.",
      tags: ["Campañas", "Automatización", "Segmentación", "A/B Testing"],
    },
    {
      title: "SEO Content",
      icon: SearchCode,
      description:
        "Contenido optimizado para buscadores que atrae tráfico orgánico de calidad con palabras clave investigadas.",
      tags: ["Keyword research", "Contenido", "Meta tags", "Estructura"],
    },
    {
      title: "Análisis de Datos",
      icon: BarChart3,
      description:
        "Interpretación automática de tus métricas con recomendaciones accionables para mejorar resultados.",
      tags: ["Dashboards", "Reportes", "KPIs", "Insights"],
    },
    {
      title: "Campañas de Anuncios",
      icon: TrendingUp,
      description:
        "Creación, gestión y optimización de campañas publicitarias en Google, Meta y más plataformas.",
      tags: ["Google Ads", "Meta Ads", "Optimización", "Remarketing"],
    },
    {
      title: "Diseño Gráfico",
      icon: Paintbrush,
      description:
        "Creación automática de imágenes, gráficos, banners y material visual para todas tus necesidades de marketing.",
      tags: ["Banners", "Posts", "Infografías", "Thumbnails"],
    },
    {
      title: "E-commerce Marketing",
      icon: ShoppingCart,
      description:
        "Estrategias específicas para tiendas online, incluyendo optimización de fichas de producto y campañas de venta.",
      tags: ["Shopify", "WooCommerce", "Abandonos", "Cross-selling"],
    },
    {
      title: "Automatización de Marketing",
      icon: Zap,
      description:
        "Flujos de trabajo automatizados que se ejecutan 24/7 para nurturing, onboarding y conversión de clientes.",
      tags: ["Workflows", "Triggers", "Journeys", "Lead scoring"],
    },
  ];

  // Datos de casos de estudio
  const caseStudies = [
    {
      title: "Aumento de 347% en ventas por email",
      company: "RetailMaster",
      results:
        "Incrementaron sus tasas de apertura al 32% y duplicaron conversiones en 30 días.",
      tags: ["Email Marketing", "E-commerce", "Copywriting"],
    },
    {
      title: "10X en tráfico orgánico en 90 días",
      company: "TechSolutions",
      results:
        "De 5,000 a 50,000 visitas mensuales con contenido SEO generado por IA.",
      tags: ["SEO", "Contenido", "B2B"],
    },
    {
      title: "ROI de 1,200% en campañas sociales",
      company: "FashionBrand",
      results:
        "Reducción del 65% en coste de adquisición con copys y creatividades optimizadas.",
      tags: ["Social Ads", "Diseño", "E-commerce"],
    },
  ];

  // Datos para testimonios
  const testimonials = [
    {
      name: "Ana Martínez",
      role: "CMO",
      company: "GrowthRetail",
      testimonial:
        "Emma AI ha transformado cómo hacemos marketing. Lo que antes nos tomaba semanas, ahora ocurre en minutos con mejores resultados. Es como tener un departamento de marketing completo trabajando 24/7.",
    },
    {
      name: "Carlos Vega",
      role: "Fundador",
      company: "StartupConnect",
      testimonial:
        "Multiplicamos nuestras conversiones con la mitad del presupuesto. Las campañas que genera Emma son increíblemente efectivas, parece que conoce a nuestra audiencia mejor que nosotros.",
    },
    {
      name: "Laura Sánchez",
      role: "Marketing Director",
      company: "GlobalServices",
      testimonial:
        "La velocidad de implementación es impresionante. Pedimos una campaña completa el viernes y el lunes ya estaba generando leads cualificados. El ROI es simplemente incomparable.",
    },
  ];

  // Para la caja de búsqueda de "pedir lo que quieras"
  const [searchQuery, setSearchQuery] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [suggestions, setSuggestions] = useState([
    "Crear una campaña de email para Black Friday",
    "Generar 10 posts para Instagram sobre nuestro nuevo producto",
    "Diseñar un funnel de ventas para servicio de consultoría",
    "Analizar el rendimiento de nuestros anuncios de Facebook",
  ]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Aquí iría la lógica para procesar la búsqueda
      window.location.href = `/app/create?prompt=${encodeURIComponent(searchQuery)}`;
    }
  };

  return (
    <EmmaLayout>
      {/* Hero ultra moderno con ondas y gradientes */}
      <section className="py-20 relative overflow-hidden">
        {/* Formas decorativas */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-purple-200 opacity-50 blur-3xl"></div>
          <div className="absolute top-80 left-40 w-72 h-72 rounded-full bg-blue-200 opacity-30 blur-3xl"></div>
          <div className="absolute -bottom-20 right-20 w-64 h-64 rounded-full bg-pink-200 opacity-40 blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-purple-600 via-pink-500 to-blue-500 bg-clip-text text-transparent">
                LA PLATAFORMA DE EMMA AI LO HACE
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 mb-10">
                Pídenos lo que quieres y lo tendrás en menos de 48 horas.
                <br />
                Estrategias de marketing optimizadas por IA, sin el costo de una
                agencia.
              </p>
            </motion.div>

            {/* Gran caja de búsqueda/prompt */}
            <div className="bg-white rounded-2xl shadow-2xl p-4 md:p-8 max-w-3xl mx-auto relative overflow-hidden border border-gray-100">
              <div className="absolute -top-10 -left-10 w-40 h-40 bg-purple-100 rounded-full opacity-50"></div>
              <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-pink-100 rounded-full opacity-50"></div>

              <h2 className="text-2xl font-bold mb-6 relative">
                ¿Qué necesitas hoy?
              </h2>

              <form onSubmit={handleSearch} className="relative z-10">
                <div className="relative">
                  <Textarea
                    placeholder="Ej: Crea una campaña de email para lanzamiento de producto con 5 emails secuenciales..."
                    className="w-full p-4 text-lg border-2 border-gray-200 focus:border-purple-500 rounded-xl h-24"
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      setIsTyping(e.target.value.length > 0);
                    }}
                  />

                  <Button
                    type="submit"
                    className="absolute bottom-3 right-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg px-6 flex items-center"
                  >
                    <Sparkles className="mr-2 h-4 w-4" />
                    Crear
                  </Button>
                </div>

                {isTyping && (
                  <div className="mt-4 bg-white rounded-xl shadow-lg border border-gray-100 p-2 absolute w-full z-20">
                    <p className="text-sm text-gray-500 mb-2 px-2">
                      Sugerencias:
                    </p>
                    {suggestions.map((suggestion, index) => (
                      <div
                        key={index}
                        className="p-2 hover:bg-purple-50 rounded-md cursor-pointer transition-colors text-left text-gray-700"
                        onClick={() => setSearchQuery(suggestion)}
                      >
                        {suggestion}
                      </div>
                    ))}
                  </div>
                )}
              </form>
            </div>
          </div>

          {/* Logos de clientes/integraciones */}
          <div className="mt-16">
            <p className="text-center text-gray-500 mb-6">
              EMPRESAS QUE CONFÍAN EN NOSOTROS
            </p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-70">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-8 w-24 bg-gray-300 rounded-md"></div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Servicios en carrusel dinámico */}
      <section id="servicios" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-purple-100 text-purple-700 hover:bg-purple-200">
              SERVICIOS
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Todo lo que una agencia hace, en un solo lugar
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Marketing completo impulsado por inteligencia artificial, sin
              equipos costosos ni largos procesos de implementación.
            </p>
          </div>

          <ServiceCarousel services={marketingServices} />
        </div>
      </section>

      {/* Sección de características/beneficios */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div>
              <Badge className="mb-4 bg-purple-100 text-purple-700 hover:bg-purple-200">
                POR QUÉ EMMA
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Resultados superlativos a una fracción del costo
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Emma AI combina inteligencia artificial avanzada con años de
                estrategias de marketing probadas para entregar resultados que
                las agencias tradicionales no pueden igualar.
              </p>

              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">
                      Velocidad inigualable
                    </h3>
                    <p className="text-gray-600">
                      Tareas que toman días se completan en minutos, con
                      resultados listos para implementar.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">
                      Optimización continua
                    </h3>
                    <p className="text-gray-600">
                      Aprendizaje constante de tus resultados para mejorar cada
                      campaña automáticamente.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Escala ilimitada</h3>
                    <p className="text-gray-600">
                      Sin límite de proyectos o iteraciones, escala tu marketing
                      sin aumentar costos.
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Integración total</h3>
                    <p className="text-gray-600">
                      Se conecta con todas tus herramientas y plataformas
                      existentes sin fricciones.
                    </p>
                  </div>
                </div>
              </div>

              <Button className="mt-8 bg-purple-600 hover:bg-purple-700 text-white">
                Descubrir más beneficios
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-200 to-pink-200 rounded-3xl transform rotate-3 opacity-70"></div>
              <div className="relative bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 p-6">
                <div className="flex items-center justify-between mb-6">
                  <Badge className="bg-purple-100 text-purple-700">
                    Resultado en tiempo real
                  </Badge>
                  <div className="text-sm text-gray-500 flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>Hace 2 minutos</span>
                  </div>
                </div>

                <h3 className="text-xl font-bold mb-4">
                  Campaña de Email Generada
                </h3>

                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      Email #1: Bienvenida
                    </div>
                    <div className="text-gray-800">
                      Asunto: Tu transformación empieza ahora [Nombre] 🚀
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      Email #2: Caso de éxito
                    </div>
                    <div className="text-gray-800">
                      Asunto: De cero a héroe: Cómo [Empresa] logró [Resultado]
                      ✨
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm font-medium text-gray-500 mb-1">
                      Email #3: Promoción
                    </div>
                    <div className="text-gray-800">
                      Asunto: ⏰ Solo 24 horas: 20% descuento exclusivo para ti
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-between items-center">
                  <div className="flex items-center">
                    <Rocket className="h-5 w-5 text-purple-600 mr-2" />
                    <span className="text-sm font-medium">
                      Tasa de apertura prevista: 32%
                    </span>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    className="text-gray-500 border-gray-300"
                  >
                    Ver campaña completa
                  </Button>
                </div>

                <div className="mt-8 h-1.5 w-full bg-gray-100 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"
                    style={{ width: "82%" }}
                  ></div>
                </div>
                <div className="mt-2 flex justify-between text-sm">
                  <span className="text-gray-500">Progreso</span>
                  <span className="font-medium">82% Completado</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Cómo funciona */}
      <section
        id="como-funciona"
        className="py-20 bg-gradient-to-br from-purple-50 to-white"
      >
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-purple-100 text-purple-700 hover:bg-purple-200">
              CÓMO FUNCIONA
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Tan simple como pedirlo
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Emma AI convierte tus instrucciones en campañas completas con solo
              unos clics.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100 relative">
              <div className="absolute -top-4 -left-4 w-8 h-8 rounded-full bg-purple-600 text-white flex items-center justify-center font-bold">
                1
              </div>
              <div className="mb-4 text-purple-600">
                <MousePointer size={36} />
              </div>
              <h3 className="text-xl font-bold mb-2">
                Describe lo que necesitas
              </h3>
              <p className="text-gray-600">
                Explica tu proyecto o simplemente selecciona una plantilla
                pre-configurada.
              </p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100 relative md:mt-10">
              <div className="absolute -top-4 -left-4 w-8 h-8 rounded-full bg-purple-600 text-white flex items-center justify-center font-bold">
                2
              </div>
              <div className="mb-4 text-purple-600">
                <BrainCircuit size={36} />
              </div>
              <h3 className="text-xl font-bold mb-2">Emma lo crea todo</h3>
              <p className="text-gray-600">
                Nuestra IA genera campañas completas, contenido, gráficos y
                estrategias.
              </p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100 relative md:mt-20">
              <div className="absolute -top-4 -left-4 w-8 h-8 rounded-full bg-purple-600 text-white flex items-center justify-center font-bold">
                3
              </div>
              <div className="mb-4 text-purple-600">
                <Rocket size={36} />
              </div>
              <h3 className="text-xl font-bold mb-2">Implementa y escala</h3>
              <p className="text-gray-600">
                Revisa, aprueba y comparte. Todo listo para implementar en menos
                de 48 horas.
              </p>
            </div>
          </div>

          <div className="flex justify-center mt-12">
            <Button className="bg-purple-600 hover:bg-purple-700 text-white">
              Ver demo en vivo
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Casos de éxito */}
      <section id="casos" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-purple-100 text-purple-700 hover:bg-purple-200">
              RESULTADOS REALES
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Casos de éxito que hablan por sí solos
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Historias de empresas que transformaron su marketing con Emma AI.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {caseStudies.map((study, index) => (
              <CaseStudyCard
                key={index}
                title={study.title}
                company={study.company}
                results={study.results}
                tags={study.tags}
              />
            ))}
          </div>

          <div className="mt-12 text-center">
            <Button
              variant="outline"
              className="border-purple-300 text-purple-700 hover:bg-purple-50"
            >
              Ver todos los casos
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonios */}
      <section className="py-20 bg-gradient-to-br from-purple-900 to-indigo-900 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-purple-200 text-purple-800 hover:bg-purple-300">
              TESTIMONIOS
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Lo que nuestros clientes dicen
            </h2>
            <p className="text-lg text-purple-200 max-w-2xl mx-auto">
              Empresas de todos los tamaños están revolucionando su marketing
              con Emma AI.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard
                key={index}
                name={testimonial.name}
                role={testimonial.role}
                company={testimonial.company}
                testimonial={testimonial.testimonial}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Precios */}
      <section id="precios" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-purple-100 text-purple-700 hover:bg-purple-200">
              PRECIOS
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Planes que se adaptan a tu negocio
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Una fracción del costo de una agencia tradicional, con resultados
              superiores.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Plan Starter */}
            <div className="bg-white rounded-xl border-2 border-gray-200 overflow-hidden transition-all hover:border-purple-300 hover:shadow-xl">
              <div className="p-6">
                <Badge className="mb-4 bg-gray-100 text-gray-700">
                  STARTER
                </Badge>
                <h3 className="text-2xl font-bold mb-2">Para Emprendedores</h3>
                <p className="text-gray-600 mb-6">
                  Ideal para negocios pequeños y startups
                </p>

                <div className="flex items-baseline mb-6">
                  <span className="text-4xl font-bold">$99</span>
                  <span className="text-gray-500 ml-2">/mes</span>
                </div>

                <Button className="w-full bg-gray-900 hover:bg-gray-800 text-white mb-6">
                  Comenzar Gratis
                </Button>

                <ul className="space-y-3">
                  {[
                    "10 proyectos/mes",
                    "Email marketing",
                    "Social media",
                    "Copywriting básico",
                    "1 usuario",
                  ].map((feature, i) => (
                    <li key={i} className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Plan Pro */}
            <div className="bg-white rounded-xl border-2 border-purple-500 overflow-hidden shadow-xl relative -mt-4 md:-mt-6 z-10 transform md:scale-105">
              <div className="absolute top-0 left-0 right-0 bg-purple-500 text-white text-center py-1 text-sm font-medium">
                MÁS POPULAR
              </div>
              <div className="p-6 pt-10">
                <Badge className="mb-4 bg-purple-100 text-purple-700">
                  PRO
                </Badge>
                <h3 className="text-2xl font-bold mb-2">Para Negocios</h3>
                <p className="text-gray-600 mb-6">
                  Perfecto para empresas en crecimiento
                </p>

                <div className="flex items-baseline mb-6">
                  <span className="text-4xl font-bold">$299</span>
                  <span className="text-gray-500 ml-2">/mes</span>
                </div>

                <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white mb-6">
                  Comenzar 14 Días Gratis
                </Button>

                <ul className="space-y-3">
                  {[
                    "Proyectos ilimitados",
                    "Todo de Starter +",
                    "Email marketing avanzado",
                    "Campañas de anuncios",
                    "SEO completo",
                    "Diseño gráfico",
                    "5 usuarios",
                    "Soporte prioritario",
                  ].map((feature, i) => (
                    <li key={i} className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Plan Enterprise */}
            <div className="bg-white rounded-xl border-2 border-gray-200 overflow-hidden transition-all hover:border-purple-300 hover:shadow-xl">
              <div className="p-6">
                <Badge className="mb-4 bg-gray-800 text-white">
                  ENTERPRISE
                </Badge>
                <h3 className="text-2xl font-bold mb-2">Para Corporaciones</h3>
                <p className="text-gray-600 mb-6">
                  Soluciones personalizadas a gran escala
                </p>

                <div className="flex items-baseline mb-6">
                  <span className="text-4xl font-bold">Custom</span>
                </div>

                <Button
                  variant="outline"
                  className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 mb-6"
                >
                  Contactar Ventas
                </Button>

                <ul className="space-y-3">
                  {[
                    "Todo de Pro +",
                    "Branding personalizado",
                    "API dedicada",
                    "Integraciones custom",
                    "Account manager",
                    "SLA garantizado",
                    "Usuarios ilimitados",
                  ].map((feature, i) => (
                    <li key={i} className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Final */}
      <section className="py-24 bg-gradient-to-br from-purple-600 to-indigo-700 text-white relative overflow-hidden">
        {/* Elementos decorativos */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          <div className="absolute -top-20 -right-20 w-80 h-80 rounded-full bg-purple-300"></div>
          <div className="absolute bottom-40 left-20 w-40 h-40 rounded-full bg-indigo-300"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              ¿Listo para transformar tu marketing?
            </h2>
            <p className="text-xl text-purple-200 mb-10">
              Únete a cientos de empresas que ya están revolucionando su
              estrategia con Emma AI.
            </p>

            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button className="bg-white text-purple-700 hover:bg-gray-100 text-lg px-8 py-6">
                Comenzar Ahora
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                className="border-purple-300 text-white hover:bg-purple-500 text-lg px-8 py-6"
              >
                Ver Demo
              </Button>
            </div>

            <div className="mt-8 flex items-center justify-center text-purple-200">
              <Clock className="h-5 w-5 mr-2" />
              <span>Implementación en menos de 48 horas</span>
            </div>
          </div>
        </div>
      </section>
    </EmmaLayout>
  );
}
