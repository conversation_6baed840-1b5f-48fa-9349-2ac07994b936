import { motion } from "framer-motion";
import { useLocation } from "wouter";
import Header from "@/components/landing/header";
import Hero from "@/components/landing/hero";
import ValueProposition from "@/components/landing/value-proposition";
import AutomatedWorkforce from "@/components/landing/automated-workforce";
import ProblemSolution from "@/components/landing/problem-solution";
import HowItWorksNew from "@/components/landing/how-it-works-new";
import AgentShowcase from "@/components/landing/agent-showcase";
import MarketingTools from "@/components/landing/marketing-tools";
import AIStudios from "@/components/landing/ai-studios";
import HumanServices from "@/components/landing/human-services";
import Comparison from "@/components/landing/comparison";
import AgentDetail from "@/components/landing/agent-detail";
import Testimonials from "@/components/landing/testimonials";
import Pricing from "@/components/landing/pricing";
import FAQ from "@/components/landing/faq";
import CTA from "@/components/landing/cta";
import NewFooter from "@/components/landing/new-footer";
import { Button } from "@/components/ui/button";

export default function LandingPage() {
  const [, setLocation] = useLocation();

  return (
    <div className="min-h-screen bg-[#f0f0f0] overflow-x-hidden">
      <div className="fixed top-4 right-4 z-50">
        <Button
          className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white border-0 shadow-[3px_3px_0px_0px_rgba(0,0,0,0.3)]"
          onClick={() => setLocation("/login")}
        >
          Iniciar Sesión
        </Button>
      </div>

      <Header />

      <main>
        {/* Background particles and grid pattern */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(30)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute bg-black/5 rounded-full"
              style={{
                width: `${Math.random() * 16 + 4}px`,
                height: `${Math.random() * 16 + 4}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                x: [0, Math.random() * 20 - 10, 0],
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          ))}
          <div
            className="absolute inset-0"
            style={{
              backgroundImage:
                "linear-gradient(#00000008 1px, transparent 1px), linear-gradient(90deg, #00000008 1px, transparent 1px)",
              backgroundSize: "16px 16px",
            }}
          />
        </div>

        {/* Secciones de la landing page en orden estratégico */}
        <Hero />
        <ValueProposition />
        <AutomatedWorkforce />
        <ProblemSolution />
        <HowItWorksNew />
        <AgentShowcase />
        <MarketingTools />
        <AIStudios />
        <HumanServices />
        <Comparison />
        <AgentDetail />
        <Testimonials />
        <Pricing />
        <FAQ />
        <CTA />
      </main>

      <NewFooter />
    </div>
  );
}
