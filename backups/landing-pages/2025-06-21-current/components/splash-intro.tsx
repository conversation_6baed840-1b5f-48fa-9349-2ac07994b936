import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { emma<PERSON>i<PERSON><PERSON> } from "../../assets";

export default function SplashIntro() {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    // Animation duration exactly 3.33 seconds (3330ms)
    const timer = setTimeout(() => {
      setVisible(false);
    }, 3330);

    return () => clearTimeout(timer);
  }, []);

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          className="fixed inset-0 bg-white flex flex-col items-center justify-center z-[9999]"
          initial={{ opacity: 1 }}
          exit={{
            opacity: 0,
            transition: { duration: 0.5 },
          }}
        >
          <div className="flex flex-col items-center justify-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <motion.img
                src={emma<PERSON><PERSON><PERSON><PERSON>}
                alt="Emma AI Logo"
                className="h-60 w-auto"
                animate={{
                  scale: [1, 1.05, 1],
                  filter: [
                    "drop-shadow(0 0 0px rgba(59, 130, 246, 0.5))",
                    "drop-shadow(0 0 25px rgba(59, 130, 246, 0.8))",
                    "drop-shadow(0 0 0px rgba(59, 130, 246, 0.5))",
                  ],
                }}
                transition={{
                  duration: 2.8,
                  times: [0, 0.5, 1],
                  ease: "easeInOut",
                }}
              />
            </motion.div>

            <motion.p
              className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 mt-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{
                opacity: [0, 1, 1, 0],
                y: [20, 0, 0, -20],
              }}
              transition={{
                duration: 2,
                times: [0, 0.2, 0.8, 1],
                delay: 0.7,
              }}
            >
              ¡Wow!
            </motion.p>

            <motion.div
              className="w-80 h-2 bg-gray-200 rounded-full overflow-hidden mt-8"
              initial={{ width: 0 }}
              animate={{ width: "20rem" }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-red-500"
                initial={{ width: "0%" }}
                animate={{ width: "100%" }}
                transition={{
                  duration: 3,
                  ease: "easeInOut",
                }}
              />
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
