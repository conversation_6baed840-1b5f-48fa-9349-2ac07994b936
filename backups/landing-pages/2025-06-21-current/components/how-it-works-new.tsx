import { motion } from "framer-motion";
import { <PERSON>ap, Clock, RefreshCcw, Bar<PERSON><PERSON> } from "lucide-react";

export default function HowItWorksNew() {
  const steps = [
    {
      id: 1,
      title: "Selecciona tus agentes",
      description:
        "Elige qué agentes IA especializados necesitas para tu equipo de marketing ideal.",
      icon: <Zap size={24} />,
      color: "blue",
    },
    {
      id: 2,
      title: "Define tus objetivos",
      description:
        "Configura tus metas y la IA ajustará las estrategias para cumplirlas.",
      icon: <Clock size={24} />,
      color: "pink",
    },
    {
      id: 3,
      title: "Los agentes trabajan 24/7",
      description:
        "Tu equipo IA trabaja día y noche creando, optimizando y mejorando tus campañas.",
      icon: <RefreshCcw size={24} />,
      color: "purple",
    },
    {
      id: 4,
      title: "Analiza y escala",
      description:
        "Visualiza resultados en tiempo real y escala tu equipo según sea necesario.",
      icon: <BarChart size={24} />,
      color: "green",
    },
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Cómo Funciona
          </h2>
          <p className="text-xl max-w-2xl mx-auto font-bold">
            <span className="bg-purple-100 px-2 py-1 rounded-lg border-2 border-black">
              La IA maneja todo
            </span>{" "}
            mientras tú te enfocas en hacer crecer tu negocio
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {steps.map((step, index) => (
            <motion.div
              key={step.id}
              className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 h-full relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              whileHover={{
                y: -10,
                boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
              }}
            >
              <div
                className={`absolute -top-4 -left-4 w-8 h-8 bg-${step.color}-500 rounded-full flex items-center justify-center border-2 border-black font-black text-white`}
              >
                {step.id}
              </div>

              <div
                className={`w-14 h-14 mb-4 bg-${step.color}-100 rounded-xl border-2 border-black flex items-center justify-center`}
              >
                <motion.div
                  className={`text-${step.color}-500`}
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 5, 0, -5, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: index * 0.2,
                  }}
                >
                  {step.icon}
                </motion.div>
              </div>

              <h3 className="text-xl font-black mb-2">{step.title}</h3>
              <p className="text-gray-700">{step.description}</p>

              <motion.div
                className={`absolute -bottom-2 -right-2 w-5 h-5 bg-${step.color}-300 rounded-full border border-black`}
                animate={{
                  scale: [1, 1.4, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: index * 0.3,
                }}
              />
            </motion.div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <motion.button
              className="bg-black text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-white hover:text-black transition-all duration-300"
              whileHover={{
                y: -5,
                boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
              }}
              whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
            >
              Comienza con Marketing IA
            </motion.button>
          </motion.div>
        </div>
      </div>

      {/* Quitamos la línea de conexión entre pasos que se veía rara */}
    </section>
  );
}
