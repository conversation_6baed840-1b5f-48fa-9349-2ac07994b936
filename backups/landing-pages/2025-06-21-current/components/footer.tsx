import { motion } from "framer-motion";
import { Link } from "wouter";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Plataforma",
      links: [
        { name: "Características", href: "#caracteristicas" },
        { name: "<PERSON><PERSON><PERSON>", href: "#precios" },
        { name: "Marketplace", href: "#" },
        { name: "Tu<PERSON><PERSON>", href: "#" },
      ],
    },
    {
      title: "Recursos",
      links: [
        { name: "Blog", href: "#" },
        { name: "<PERSON><PERSON><PERSON>", href: "#" },
        { name: "Webinars", href: "#" },
        { name: "API", href: "#" },
      ],
    },
    {
      title: "Empresa",
      links: [
        { name: "Sobre Nosotros", href: "#" },
        { name: "Contacto", href: "#" },
        { name: "T<PERSON>rminos de Servicio", href: "#" },
        { name: "Política de Privacidad", href: "#" },
      ],
    },
  ];

  return (
    <footer className="bg-white py-12 border-t-4 border-black">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <Link
              href="/"
              className="text-2xl font-black bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-transparent bg-clip-text mb-4 inline-block"
            >
              MarketingIA
            </Link>
            <p className="text-gray-600">
              Tu agencia de marketing virtual completa, potenciada por
              inteligencia artificial avanzada.
            </p>
          </motion.div>

          {footerSections.map((section, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 * (i + 1), duration: 0.5 }}
            >
              <h4 className="font-bold mb-4">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link, j) => (
                  <li key={j}>
                    <a
                      href={link.href}
                      className="text-gray-600 hover:text-black transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-12 pt-8 border-t border-gray-200 text-center text-gray-600"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          <p>© {currentYear} MarketingIA. Todos los derechos reservados.</p>
        </motion.div>
      </div>
    </footer>
  );
}
