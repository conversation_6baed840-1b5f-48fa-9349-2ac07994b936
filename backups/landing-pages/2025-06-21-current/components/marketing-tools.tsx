import { motion } from "framer-motion";
import {
  Search,
  BarChart3,
  Users,
  Sparkles,
  Calendar,
  FileSearch,
  Palette,
  Layout,
  Image as ImageIcon,
  MessageCircle,
  Heart,
  HeartIcon,
} from "lucide-react";
import { Link, useLocation } from "wouter";

// Importar imágenes de los agentes
import aiAnalyzerCharacter from "../../assets/ai-analyzer-character.png";
import novaAgent from "../../assets/images/nova-agent.jpg";
import seoxAgent from "../../assets/images/seox-agent.png";
import emmaProfile from "../../assets/emma-profile.png";

// Importar configuración centralizada
import { getToolRoute, isToolImplemented } from "../../data/ai-tools-data";
import { useFavorites } from "../../hooks/use-favorites";

export default function MarketingTools() {
  const [, setLocation] = useLocation();
  const { isFavorite, toggleFavorite } = useFavorites();

  const tools = [
    {
      id: "headline-analyzer",
      name: "Headline Analyzer",
      description:
        "Analiza y optimiza tus títulos para máximo impacto y conversión",
      icon: <Search className="w-6 h-6 text-blue-600" />,
      bgColor: "bg-blue-100",
      textColor: "text-blue-600",
      bgHoverColor: "bg-blue-50",
      textHoverColor: "text-blue-700",
      agentImage: novaAgent,
      agentName: "Nova",
      path: getToolRoute("headline-analyzer"),
    },
    {
      id: "seo-analyzer",
      name: "Analizador SEO",
      description: "Análisis completo de SEO para mejorar tu posicionamiento",
      icon: <FileSearch className="w-6 h-6 text-green-600" />,
      bgColor: "bg-green-100",
      textColor: "text-green-600",
      bgHoverColor: "bg-green-50",
      textHoverColor: "text-green-700",
      agentImage: seoxAgent,
      agentName: "Seox",
      path: getToolRoute("seo-analyzer"),
    },
    {
      id: "buyer-persona-generator",
      name: "Generador de Buyer Personas",
      description: "Crea perfiles detallados de tu audiencia ideal",
      icon: <Users className="w-6 h-6 text-yellow-600" />,
      bgColor: "bg-yellow-100",
      textColor: "text-yellow-600",
      bgHoverColor: "bg-yellow-50",
      textHoverColor: "text-yellow-700",
      agentImage: emmaProfile,
      agentName: "Emma",
      path: getToolRoute("buyer-persona-generator"),
    },
    {
      id: "focus-group-simulator",
      name: "Simulador de Focus Group",
      description: "Simula un focus group virtual con perfiles personalizados",
      icon: <Sparkles className="w-6 h-6 text-pink-600" />,
      bgColor: "bg-pink-100",
      textColor: "text-pink-600",
      bgHoverColor: "bg-pink-50",
      textHoverColor: "text-pink-700",
      agentImage: emmaProfile,
      agentName: "Emma",
      path: getToolRoute("focus-group-simulator"),
    },
    {
      id: "design-complexity-analyzer",
      name: "Analizador de Complejidad Visual",
      description: "Evalúa la complejidad visual de tus diseños con IA",
      icon: <Layout className="w-6 h-6 text-purple-600" />,
      bgColor: "bg-purple-100",
      textColor: "text-purple-600",
      bgHoverColor: "bg-purple-50",
      textHoverColor: "text-purple-700",
      agentImage: aiAnalyzerCharacter,
      agentName: "VisuAI",
      path: getToolRoute("design-complexity-analyzer"),
    },
    {
      id: "color-palette-generator",
      name: "Generador de Paletas",
      description: "Crea paletas de colores armónicas para tus campañas",
      icon: <Palette className="w-6 h-6 text-orange-600" />,
      bgColor: "bg-orange-100",
      textColor: "text-orange-600",
      bgHoverColor: "bg-orange-50",
      textHoverColor: "text-orange-700",
      agentImage: aiAnalyzerCharacter,
      agentName: "ColorAI",
      path: getToolRoute("color-palette-generator"),
    },
    {
      id: "mood-board",
      name: "Mood Board Interactivo",
      description: "Diseña tableros de inspiración visual para tus proyectos",
      icon: <ImageIcon className="w-6 h-6 text-indigo-600" />,
      bgColor: "bg-indigo-100",
      textColor: "text-indigo-600",
      bgHoverColor: "bg-indigo-50",
      textHoverColor: "text-indigo-700",
      agentImage: aiAnalyzerCharacter,
      agentName: "MoodAI",
      path: getToolRoute("mood-board"),
    },
    {
      id: "generador-posts-profesional",
      name: "Generador de Posts",
      description: "Crea contenido para redes sociales con un solo clic",
      icon: <MessageCircle className="w-6 h-6 text-cyan-600" />,
      bgColor: "bg-cyan-100",
      textColor: "text-cyan-600",
      bgHoverColor: "bg-cyan-50",
      textHoverColor: "text-cyan-700",
      agentImage: novaAgent,
      agentName: "Nova",
      path: getToolRoute("generador-posts-profesional"),
    },
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Toolbox IA Para Marketers
          </h2>
          <p className="text-xl max-w-2xl mx-auto font-bold">
            Herramientas avanzadas que potencian tu estrategia de marketing
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 h-full relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.1 * index }}
              whileHover={{
                y: -10,
                boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
                transition: { duration: 0.2 },
              }}
            >
              <div className="flex items-start mb-4 relative z-10">
                <div
                  className={`w-12 h-12 ${tool.bgColor} rounded-lg border-2 border-black flex items-center justify-center mr-4`}
                >
                  {tool.icon}
                </div>
                <div className="flex-1">
                  <h3 className={`text-xl font-black ${tool.textColor}`}>
                    {tool.name}
                  </h3>
                </div>
                {/* Botón de favoritos */}
                <motion.button
                  className={`p-2 rounded-lg border-2 border-black ${
                    isFavorite(tool.id)
                      ? 'bg-red-100 text-red-600'
                      : 'bg-gray-100 text-gray-600'
                  } hover:bg-red-50 hover:text-red-600 transition-colors`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFavorite(tool.id, tool.name);
                  }}
                >
                  <Heart
                    className="w-4 h-4"
                    fill={isFavorite(tool.id) ? 'currentColor' : 'none'}
                  />
                </motion.button>
              </div>

              <p className="text-gray-700 mb-4 relative z-10">
                {tool.description}
              </p>

              {/* Agent card */}
              <div className="flex items-center mb-5 bg-gray-50 p-3 rounded-lg border-2 border-gray-200 relative z-10">
                <div className="w-12 h-12 rounded-full border-2 border-gray-300 overflow-hidden mr-3 flex-shrink-0">
                  <img
                    src={tool.agentImage}
                    alt={`${tool.agentName} IA Agent`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <p className="font-bold text-sm">{tool.agentName}</p>
                  <p className="text-xs text-gray-600">
                    Asistente IA especializado
                  </p>
                </div>
              </div>

              <motion.button
                className={`w-full py-3 px-4 ${tool.bgHoverColor} rounded-lg ${tool.textHoverColor} font-bold text-sm border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)] mt-auto relative z-10`}
                whileHover={{
                  y: -3,
                  boxShadow: "5px 5px 0px 0px rgba(0,0,0,0.9)",
                }}
                onClick={() => setLocation(tool.path)}
              >
                Probar Herramienta
              </motion.button>

              {/* Decorative element */}
              <div className="absolute -bottom-10 -right-10 w-32 h-32 opacity-10 rotate-12">
                <img
                  src={tool.agentImage}
                  alt=""
                  className="w-full h-full object-cover"
                />
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <motion.button
            className="bg-black text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-white hover:text-black transition-all duration-300"
            whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
            whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
            onClick={() => setLocation("/dashboard/herramientas-marketing")}
          >
            Explorar Todas las Herramientas →
          </motion.button>
        </motion.div>
      </div>

      {/* Elementos de fondo */}
      <div className="absolute top-20 left-0 w-64 h-64 bg-blue-200 rounded-full filter blur-3xl opacity-20 transform -translate-x-1/2"></div>
      <div className="absolute bottom-10 right-0 w-64 h-64 bg-purple-200 rounded-full filter blur-3xl opacity-20 transform translate-x-1/3"></div>
    </section>
  );
}
