import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ChevronLeft,
  ChevronRight,
  Award,
  Zap,
  TrendingUp,
} from "lucide-react";

export default function AgentDetail() {
  const [currentIndex, setCurrentIndex] = useState(0);

  const agents = [
    {
      title: "AI Social Media Manager",
      avatar: "📱",
      color: "blue",
      specialty: "Gestión de redes sociales y engagement",
      advantage: "Consistencia y optimización 24/7",
      impact: "Incremento de 300% en engagement",
      description:
        "Crea, programa y optimiza contenido para todas tus redes sociales. Analiza tendencias, interacciona con tu audiencia y maximiza tu alcance orgánico.",
    },
    {
      title: "AI Content Strategist",
      avatar: "📊",
      color: "pink",
      specialty: "Estrategia de contenido data-driven",
      advantage: "Análisis predictivo de tendencias",
      impact: "Reducción de 70% en CAC",
      description:
        "Desarrolla estrategias de contenido basadas en datos. Identifica oportunidades, analiza a la competencia y crea planes editoriales con alto impacto.",
    },
    {
      title: "AI Email Marketing Specialist",
      avatar: "📧",
      color: "purple",
      specialty: "Conversión por email marketing",
      advantage: "Testing A/B automático",
      impact: "Incremento de 150% en open rate",
      description:
        "Diseña campañas de email optimizadas para conversión. Segmenta audiencias, personaliza mensajes y analiza comportamiento para máximo impacto.",
    },
    {
      title: "AI Analytics Expert",
      avatar: "📈",
      color: "green",
      specialty: "Análisis de datos y optimización",
      advantage: "Insights accionables en tiempo real",
      impact: "Incremento de 200% en ROI",
      description:
        "Analiza el rendimiento de tus campañas en tiempo real. Identifica oportunidades, corrige problemas y optimiza para resultados superiores.",
    },
  ];

  const nextAgent = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === agents.length - 1 ? 0 : prevIndex + 1,
    );
  };

  const prevAgent = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? agents.length - 1 : prevIndex - 1,
    );
  };

  const currentAgent = agents[currentIndex];

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Conoce a Tu Equipo de Marketing IA
          </h2>
          <p className="text-xl max-w-2xl mx-auto font-bold">
            Especialistas digitales diseñados para impulsar tu negocio
          </p>
        </motion.div>

        <div className="relative">
          <div className="bg-white rounded-2xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] p-8 overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ duration: 0.3 }}
                className="grid grid-cols-1 md:grid-cols-12 gap-8 items-center"
              >
                {/* Izquierda: Avatar y título */}
                <div className="md:col-span-5 flex flex-col items-center md:items-start">
                  <motion.div
                    className={`w-32 h-32 bg-${currentAgent.color}-100 rounded-2xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] flex items-center justify-center mb-6`}
                    animate={{
                      rotate: [0, 5, 0, -5, 0],
                      scale: [1, 1.05, 1, 1.05, 1],
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  >
                    <span className="text-6xl">{currentAgent.avatar}</span>
                  </motion.div>

                  <h3
                    className={`text-2xl font-black text-${currentAgent.color}-500 mb-3`}
                  >
                    {currentAgent.title}
                  </h3>

                  <div className="flex space-x-2 mb-6">
                    {[...Array(5)].map((_, i) => (
                      <div
                        key={i}
                        className="w-5 h-5 bg-yellow-400 rounded-full border-2 border-black"
                      />
                    ))}
                  </div>

                  <motion.button
                    className={`bg-${currentAgent.color}-500 text-white font-black py-3 px-6 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] mt-4`}
                    whileHover={{
                      y: -5,
                      boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
                    }}
                    whileTap={{
                      y: 0,
                      boxShadow: "2px 2px 0px 0px rgba(0,0,0,0.9)",
                    }}
                  >
                    Asignar a Trabajar
                  </motion.button>
                </div>

                {/* Derecha: Características y descripción */}
                <div className="md:col-span-7">
                  <p className="text-lg mb-8">{currentAgent.description}</p>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                    {[
                      {
                        label: "Specialty",
                        value: currentAgent.specialty,
                        icon: <Award size={16} />,
                      },
                      {
                        label: "Advantage",
                        value: currentAgent.advantage,
                        icon: <Zap size={16} />,
                      },
                      {
                        label: "Impact",
                        value: currentAgent.impact,
                        icon: <TrendingUp size={16} />,
                      },
                    ].map((item, idx) => (
                      <motion.div
                        key={idx}
                        className={`bg-${currentAgent.color}-50 p-4 rounded-lg border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)]`}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * idx, duration: 0.3 }}
                        whileHover={{ y: -3 }}
                      >
                        <div className="flex items-center mb-2">
                          <div
                            className={`w-6 h-6 bg-${currentAgent.color}-200 rounded-full flex items-center justify-center border border-black mr-2`}
                          >
                            {item.icon}
                          </div>
                          <span className="text-xs font-bold text-gray-500">
                            {item.label}
                          </span>
                        </div>
                        <div className="font-bold text-sm">{item.value}</div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Navegación del carrusel */}
            <div className="flex items-center justify-between mt-8">
              <div className="text-sm font-medium">
                <span className="opacity-50">
                  Desliza para conocer más agentes{" "}
                </span>
                <span className="text-black font-bold">
                  {currentIndex + 1}/{agents.length}
                </span>
              </div>

              <div className="flex space-x-2">
                <motion.button
                  onClick={prevAgent}
                  className="w-10 h-10 bg-gray-100 rounded-full border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] flex items-center justify-center"
                  whileHover={{
                    scale: 1.1,
                    backgroundColor: "var(--gray-200)",
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  <ChevronLeft size={20} />
                </motion.button>
                <motion.button
                  onClick={nextAgent}
                  className="w-10 h-10 bg-gray-100 rounded-full border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] flex items-center justify-center"
                  whileHover={{
                    scale: 1.1,
                    backgroundColor: "var(--gray-200)",
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  <ChevronRight size={20} />
                </motion.button>
              </div>
            </div>
          </div>
        </div>

        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <motion.button
            className="bg-black text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-white hover:text-black transition-all duration-300"
            whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
            whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
          >
            Construir Mi Equipo IA →
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
