import { motion } from "framer-motion";

export default function Testimonials() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Directora de Marketing, TechVision",
      quote:
        "MarketingIA ha transformado completamente nuestra estrategia digital. Lo que antes tomaba semanas, ahora se logra en horas con resultados sorprendentemente buenos. El ROI ha sido impresionante.",
      avatar: "MS",
      color: "blue",
      stars: 5,
    },
    {
      name: "<PERSON>",
      role: "Fundador, EcoStartup",
      quote:
        "Como fundador con recursos limitados, esta plataforma me ha permitido competir con marcas mucho más grandes. El equipo IA trabaja incansablemente y los resultados son consistentemente excelentes.",
      avatar: "JL",
      color: "green",
      stars: 5,
    },
    {
      name: "<PERSON>",
      role: "CEO, Agencia Digital Nexo",
      quote:
        "Hemos integrado MarketingIA como un socio estratégico para nuestra agencia. Nos permite entregar más valor a nuestros clientes mientras reducimos costos operativos en un 40%.",
      avatar: "AR",
      color: "purple",
      stars: 4,
    },
  ];

  const companies = ["EMPRESA 1", "EMPRESA 2", "EMPRESA 3", "EMPRESA 4"];

  return (
    <section id="testimonios" className="py-20 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Lo Que Dicen Nuestros Clientes
          </h2>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] transition-all duration-200"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{ delay: index * 0.1, duration: 0.5 }}
              whileHover={{ y: -5 }}
            >
              <div className="flex items-center mb-4">
                <motion.div
                  className={`w-16 h-16 bg-${testimonial.color}-100 rounded-full border-2 border-black overflow-hidden mr-4 flex items-center justify-center`}
                  whileHover={{ scale: 1.1, rotate: 10 }}
                >
                  <span className={`font-black text-${testimonial.color}-600`}>
                    {testimonial.avatar}
                  </span>
                </motion.div>
                <div>
                  <h4 className="font-black">{testimonial.name}</h4>
                  <p className="text-sm text-gray-600">{testimonial.role}</p>
                </div>
              </div>
              <div className="mb-4">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-5 w-5 ${i < testimonial.stars ? "text-yellow-500" : "text-gray-300"}`}
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
              </div>
              <p className="italic">{testimonial.quote}</p>
            </motion.div>
          ))}
        </div>

        {/* Trusted By Section */}
        <motion.div
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <h3 className="text-xl font-black mb-8">
            Empresas que confían en nosotros
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {companies.map((company, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-lg border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] h-20 flex items-center justify-center"
                whileHover={{
                  y: -5,
                  boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
                }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3 + index * 0.1, duration: 0.5 }}
              >
                <div className="font-black text-gray-400 text-xl">
                  {company}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
