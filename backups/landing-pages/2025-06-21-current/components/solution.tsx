import { motion } from "framer-motion";

export default function Solution() {
  const solutions = [
    {
      number: "1",
      title: "Automatización Inteligente",
      description:
        "Procesos de marketing que tomaban semanas ahora se completan en minutos u horas.",
      color: "blue",
    },
    {
      number: "2",
      title: "Equipo IA 24/7",
      description:
        "Accede a especialistas virtuales en cualquier momento, sin limitaciones de disponibilidad o agotamiento.",
      color: "pink",
    },
    {
      number: "3",
      title: "Costos Reducidos",
      description:
        "Fracción del precio de agencias tradicionales o equipos internos, sin sacrificar calidad.",
      color: "green",
    },
    {
      number: "4",
      title: "Escalabilidad Ilimitada",
      description:
        "Amplía tus operaciones de marketing instantáneamente sin los desafíos de contratación o capacitación.",
      color: "purple",
    },
  ];

  return (
    <section id="solucion" className="py-20 relative">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Nuestra Solución: MarketingIA
          </h2>
          <p className="text-xl font-bold max-w-3xl mx-auto">
            La primera agencia de marketing operada por un equipo de
            Inteligencia Artificial
          </p>
        </motion.div>

        <div className="max-w-6xl mx-auto">
          <motion.div
            className="bg-white rounded-2xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-8 mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-50px" }}
            transition={{ duration: 0.5 }}
            whileHover={{ boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
          >
            <div className="flex flex-col lg:flex-row gap-8 items-center">
              <motion.div
                className="w-full lg:w-1/2"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2, duration: 0.5 }}
              >
                <div className="aspect-w-4 aspect-h-3 bg-blue-50 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] overflow-hidden">
                  <div className="p-6 flex items-center justify-center">
                    <div className="grid grid-cols-2 gap-4 w-full">
                      {[
                        {
                          color: "blue",
                          title: "Marketing Strategy",
                          subtitle: "Plan → Execute → Optimize",
                        },
                        {
                          color: "pink",
                          title: "Creative Content",
                          subtitle: "Design → Copy → Publish",
                        },
                        {
                          color: "green",
                          title: "Analytics & Reports",
                          subtitle: "Track → Analyze → Report",
                        },
                        {
                          color: "purple",
                          title: "Campaign Management",
                          subtitle: "Schedule → Monitor → Adjust",
                        },
                      ].map((item, index) => (
                        <motion.div
                          key={index}
                          className={`bg-${item.color}-100 rounded-lg border-2 border-black p-3 h-32`}
                          initial={{ opacity: 0, scale: 0.9 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          viewport={{ once: true }}
                          transition={{ delay: 0.3 + index * 0.1 }}
                          whileHover={{ y: -5 }}
                        >
                          <motion.div
                            className={`w-10 h-10 bg-${item.color}-500 rounded-lg border-2 border-black flex items-center justify-center text-white font-bold`}
                            animate={{
                              rotate: [0, 5, 0, -5, 0],
                              scale: [1, 1.1, 1, 1.1, 1],
                            }}
                            transition={{
                              duration: 5,
                              repeat: Infinity,
                              delay: index * 0.5,
                            }}
                          >
                            AI
                          </motion.div>
                          <div className="mt-2 text-sm font-bold">
                            {item.title}
                          </div>
                          <div className="mt-1 text-xs">{item.subtitle}</div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>

              <div className="w-full lg:w-1/2 space-y-6">
                {solutions.map((solution, index) => (
                  <motion.div
                    key={index}
                    className={`bg-${solution.color}-50 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] p-4`}
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.2 + index * 0.1, duration: 0.5 }}
                    whileHover={{ x: 5 }}
                  >
                    <h3 className="text-lg font-black flex items-center">
                      <span
                        className={`w-8 h-8 bg-${solution.color}-500 rounded-lg border-2 border-black flex items-center justify-center text-white font-bold mr-3`}
                      >
                        {solution.number}
                      </span>
                      {solution.title}
                    </h3>
                    <p className="mt-2 pl-11">{solution.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
