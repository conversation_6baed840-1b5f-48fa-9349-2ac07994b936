import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";

export default function AgentShowcase() {
  const agents = [
    {
      title: "AI Copywriter",
      description: "Crea textos persuasivos, posts, emails y contenido SEO",
      icon: "📝",
      color: "blue",
    },
    {
      title: "AI Designer",
      description:
        "Diseña gráficos, banners y recursos visuales para cualquier plataforma",
      icon: "🎨",
      color: "pink",
    },
    {
      title: "AI Ad Manager",
      description:
        "Optimiza campañas publicitarias para máximo ROI en todos los canales",
      icon: "📊",
      color: "purple",
    },
    {
      title: "AI Social Media",
      description:
        "Gestiona tus redes sociales con contenido relevante y atractivo",
      icon: "💬",
      color: "green",
    },
    {
      title: "AI SEO Expert",
      description:
        "Optimiza tu contenido para los motores de búsqueda y aumenta tu ranking",
      icon: "🔍",
      color: "yellow",
    },
    {
      title: "AI Email Marketer",
      description:
        "Desarrolla campañas de email marketing optimizadas para conversión",
      icon: "📧",
      color: "red",
    },
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Contrata Empleados IA Para Tu Negocio
          </h2>
          <p className="text-xl max-w-2xl mx-auto font-bold">
            Especialistas que trabajan sin descanso para impulsar tu estrategia
            de marketing
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {agents.map((agent, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl border-3 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] p-6 h-full"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.4, delay: 0.1 * index }}
              whileHover={{
                y: -10,
                boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
                transition: { duration: 0.2 },
              }}
            >
              <div className="flex justify-between items-start mb-4">
                <div
                  className={`w-14 h-14 bg-${agent.color}-100 rounded-xl border-2 border-black flex items-center justify-center`}
                >
                  <motion.div
                    className="text-2xl"
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 10, 0],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: index * 0.2,
                    }}
                  >
                    {agent.icon}
                  </motion.div>
                </div>

                <motion.div
                  className={`w-8 h-8 bg-${agent.color}-500 rounded-full border-2 border-black flex items-center justify-center text-white font-bold text-xs`}
                  whileHover={{ scale: 1.2, rotate: 10 }}
                >
                  AI
                </motion.div>
              </div>

              <h3 className={`text-xl font-black mb-2 text-${agent.color}-500`}>
                {agent.title}
              </h3>
              <p className="text-gray-700 text-sm mb-4">{agent.description}</p>

              <motion.button
                className={`w-full py-2 mt-auto bg-${agent.color}-100 rounded-lg text-${agent.color}-700 font-bold text-sm border-2 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)] flex items-center justify-center`}
                whileHover={{
                  y: -3,
                  boxShadow: "5px 5px 0px 0px rgba(0,0,0,0.9)",
                  backgroundColor: `var(--${agent.color}-200)`,
                }}
              >
                <span>Contratar Agente</span>
                <ArrowRight size={14} className="ml-1" />
              </motion.button>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <motion.button
            className="bg-purple-500 text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-purple-600 transition-all duration-300"
            whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
            whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
          >
            Construye Tu Equipo IA Ahora
          </motion.button>
        </motion.div>
      </div>

      {/* Elementos de fondo */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-200 rounded-full filter blur-3xl opacity-20 transform translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-200 rounded-full filter blur-3xl opacity-20 transform -translate-x-1/2 translate-y-1/2"></div>
    </section>
  );
}
