import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>,
  ArrowR<PERSON>,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  PlusCircle,
  CheckCircle2,
} from "lucide-react";

export default function AIStudios() {
  return (
    <section className="py-20 relative overflow-hidden bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="inline-block text-3xl sm:text-4xl font-black bg-white px-6 py-3 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] mb-6">
            Ads Central: Campañas + Creativos
          </h2>
          <p className="text-xl max-w-2xl mx-auto font-bold">
            Centro de creación de anuncios con IA especializada en publicidad
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Izquierda: Imagen del dashboard de roadmaps */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="bg-white rounded-2xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)] p-6 overflow-hidden">
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-xl font-black">Biblioteca de Campañas</h3>
                <div className="flex space-x-2">
                  <motion.div
                    className="w-8 h-8 rounded-lg bg-purple-100 border-2 border-black flex items-center justify-center"
                    whileHover={{ y: -2 }}
                  >
                    <RefreshCw size={15} />
                  </motion.div>
                  <motion.div
                    className="w-8 h-8 rounded-lg bg-blue-100 border-2 border-black flex items-center justify-center"
                    whileHover={{ y: -2 }}
                  >
                    <Settings size={15} />
                  </motion.div>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                {[
                  "Campaña de Facebook Ads",
                  "Anuncios de Google Ads",
                  "Creativos para Instagram",
                  "Campañas de LinkedIn Ads",
                  "Anuncios de YouTube",
                ].map((roadmap, index) => (
                  <motion.div
                    key={index}
                    className="bg-gray-50 p-4 rounded-lg border-2 border-black flex justify-between items-center"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.1 * index, duration: 0.4 }}
                    whileHover={{
                      x: 5,
                      backgroundColor: "#f0f0ff",
                    }}
                  >
                    <div className="flex items-center">
                      <CalendarClock
                        size={18}
                        className="mr-3 text-purple-600"
                      />
                      <span className="font-bold">{roadmap}</span>
                    </div>
                    <motion.button
                      className="w-7 h-7 rounded-full bg-purple-100 border-2 border-black flex items-center justify-center"
                      whileHover={{ scale: 1.1 }}
                    >
                      <ArrowRight size={12} className="text-purple-600" />
                    </motion.button>
                  </motion.div>
                ))}

                <motion.div
                  className="p-4 rounded-lg border-2 border-dashed border-gray-400 flex justify-center items-center text-gray-400"
                  whileHover={{
                    borderColor: "#000",
                    color: "#000",
                  }}
                >
                  <PlusCircle size={18} className="mr-2" />
                  <span className="font-bold">Crear Mi Propia Campaña</span>
                </motion.div>
              </div>
            </div>

            <motion.div
              className="absolute -bottom-6 -right-6 bg-purple-300 rounded-xl border-3 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] p-4 max-w-[200px]"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.6 }}
              whileHover={{
                y: -5,
                boxShadow: "6px 6px 0px 0px rgba(0,0,0,0.9)",
              }}
            >
              <div className="font-black text-sm mb-1">+ 50 Plantillas</div>
              <div className="text-xs">
                Para diferentes plataformas y objetivos
              </div>
            </motion.div>
          </motion.div>

          {/* Derecha: Características destacadas */}
          <motion.div
            className="flex flex-col justify-center"
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="space-y-6">
              {[
                {
                  title: "Plantillas de Anuncios",
                  description:
                    "Creativos pre-diseñados listos para usar en todas las plataformas",
                },
                {
                  title: "Generación Automática",
                  description:
                    "IA especializada que crea copys, imágenes y videos publicitarios",
                },
                {
                  title: "Optimización por Plataforma",
                  description:
                    "Formatos específicos para Facebook, Google, Instagram y más",
                },
                {
                  title: "A/B Testing Inteligente",
                  description:
                    "Variaciones automáticas para maximizar el rendimiento",
                },
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="flex"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.1 * index + 0.3 }}
                >
                  <div className="mr-4 mt-1">
                    <div className="w-8 h-8 bg-green-100 rounded-full border-2 border-black flex items-center justify-center">
                      <CheckCircle2 size={16} className="text-green-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-black mb-2">{feature.title}</h3>
                    <p className="text-gray-700">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.button
              className="mt-10 bg-purple-500 text-white font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] self-start hover:bg-purple-600 transition-all duration-300"
              whileHover={{
                y: -5,
                boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)",
              }}
              whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
            >
              Explorar Ads Central →
            </motion.button>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
