# Landing Page Backup - Emma Studio

## Descripción
Este directorio contiene una copia de seguridad completa de la landing page original de Emma Studio, creada el 21 de junio de 2025.

**NOTA:** Esta es una copia de seguridad anterior. Para la copia de seguridad más reciente, consulte el directorio `/backups/landing-pages/2025-06-21-current/`

## Archivos incluidos

### Página principal
- `landing-page-backup.tsx` - Página principal de la landing (ubicada en `/pages/`)
- `emma-landing-v2-backup.tsx` - Landing page v2 (ubicada en `/pages/`)

### Componentes de la landing
Todos los componentes originales de la carpeta `/components/landing/`:

- `agent-detail.tsx` - Detalles de agentes
- `agent-showcase.tsx` - Showcase de agentes
- `ai-studios.tsx` - Sección AI Studios
- `automated-workforce.tsx` - Fuerza de trabajo automatizada
- `comparison.tsx` - Comparaciones
- `cta.tsx` - Call to action
- `faq.tsx` - Preguntas frecuentes
- `features.tsx` - Características
- `footer.tsx` - Footer
- `header.tsx` - Header
- `hero.tsx` - Sección hero
- `how-it-works-new.tsx` - Cómo funciona (nueva versión)
- `how-it-works.tsx` - Cómo funciona (versión original)
- `human-services.tsx` - Servicios humanos
- `intro-animation.tsx` - Animación de introducción
- `marketing-tools.tsx` - Herramientas de marketing
- `new-footer.tsx` - Footer nuevo
- `preloader.tsx` - Preloader
- `pricing.tsx` - Precios
- `problem-solution.tsx` - Problema y solución
- `problem.tsx` - Problema
- `solution.tsx` - Solución
- `splash-intro.tsx` - Intro splash
- `testimonials.tsx` - Testimonios
- `use-cases.tsx` - Casos de uso
- `value-proposition.tsx` - Propuesta de valor

## Cómo restaurar
Para restaurar la landing page original:

1. Copiar `landing-page-backup.tsx` a `landing-page.tsx`
2. Copiar todos los archivos de `/landing-backup/` a `/landing/`
3. Actualizar las rutas de importación en el archivo principal

## Notas
- Esta copia de seguridad preserva la funcionalidad completa de la landing page original
- Los colores de marca utilizados son #3018ef (azul) y #dd3a5a (rojo/rosa)
- Utiliza Wouter para el routing
- Implementa efectos glassmorphism y animaciones con Framer Motion
